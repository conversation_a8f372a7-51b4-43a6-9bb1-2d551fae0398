<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MinerU文档解析器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #f5f7fa 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #8ed2f5 0%, #0097de 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        h2 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            line-height: 1.6;
            position: relative;
            z-index: 1;
        }

        .main-content {
            padding: 40px;
        }

        h3 {
            font-size: 20px;
            margin-bottom: 20px;
            color: #2c3e50;
            font-weight: 600;
        }

        .upload-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px dashed #6c757d;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            cursor: pointer;
        }

        .upload-container.drag-over {
            border-color: #667eea;
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .upload-container.drag-over::before {
            opacity: 1;
            animation: shimmer 1s infinite;
        }

        .upload-container::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.6s ease;
            opacity: 0;
        }

        .upload-container:hover::before {
            opacity: 1;
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .upload-container:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }

        .file-info {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 20px;
            text-align: center;
            opacity: 0.8;
        }

        .drag-hint {
            font-size: 14px;
            color: #667eea;
            margin-top: 10px;
            opacity: 0.7;
            transition: opacity 0.3s ease;
        }

        .upload-container.drag-over .drag-hint {
            opacity: 1;
            color: #4f46e5;
            font-weight: 500;
        }

        #result {
            margin-top: 30px;
            padding: 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            display: none;
            border: 1px solid #e9ecef;
        }

        #markdown-content {
            max-height: 500px;
            overflow-y: auto;
            padding: 20px;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-top: 15px;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .markdown-body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #2c3e50;
            word-break: break-word;
            white-space: normal;
        }

        .markdown-body pre {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            padding: 20px;
            white-space: pre-wrap;
            word-break: break-word;
            overflow-wrap: anywhere;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(44, 62, 80, 0.9);
            backdrop-filter: blur(5px);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            flex-direction: column;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            border-top-color: #667eea;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .loading-text {
            margin-top: 20px;
            font-size: 18px;
            color: white;
            font-weight: 500;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            cursor: pointer;
            margin: 8px;
            display: inline-block;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        button:hover::before {
            left: 100%;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        /* 隐藏原始文件输入控件 */
        #fileInput {
            display: none;
        }

        /* 伪按钮样式，使用 label 实现点击上传 */
        .upload-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 12px 24px;
            border: none;
            cursor: pointer;
            margin: 8px;
            display: inline-block;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            border-radius: 25px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
            position: relative;
            overflow: hidden;
        }

        .upload-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .upload-btn:hover::before {
            left: 100%;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }

        #fileNames {
            margin: 15px 0;
            font-size: 14px;
            color: #6c757d;
            padding: 10px 15px;
            background: rgba(108, 117, 125, 0.1);
            border-radius: 8px;
            min-height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .upload-row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 15px;
            margin-top: 25px;
            flex-wrap: wrap;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }

        .form-group select {
            width: 100%;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .result-header {
            font-weight: 600;
            font-size: 18px;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .result-header::before {
            content: '✨';
            font-size: 20px;
        }

        #status {
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            font-weight: 500;
        }

        .success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .markdown-body table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .markdown-body th,
        .markdown-body td {
            border: 1px solid #e9ecef;
            padding: 12px 16px;
            text-align: left;
        }

        .markdown-body th {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-weight: 600;
            color: #495057;
        }
    </style>
    <script>
        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$']],
            processEscapes: true
          },
          svg: {
            fontCache: 'global'
          }
        };
    </script>
    <script src="mineru/js/tex-svg.js"></script>
</head>
<body>
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">处理中，请稍候...</div>
    </div>

    <div class="container">
        <div class="header">
            <h2>MinerU文档解析器</h2>
            <p class="subtitle">
                智能文档解析平台 - 支持将上传的文档解析为带有格式的 Markdown 文件<br>
                将包含图片、公式、表格等元素的多模态 PDF、PPT、DOCX 等文档转化为人类正常阅读顺序的 Markdown 格式
            </p>
            <button type="button" id="downloadSampleBtn">📥 下载示例文件</button>
        </div>

        <div class="main-content">
            <div class="upload-container" id="uploadContainer">
                <h3>📄 上传文件</h3>
                <div class="file-info">支持文件格式：PDF, Word, PowerPoint, 图片 (JPG, PNG)</div>
                <div class="drag-hint">💡 您可以直接拖拽文件到此区域进行上传</div>

                <form id="uploadForm">
                    <div class="form-group">
                        <label for="modelSelect">🔍 图片处理模型</label>
                        <select id="modelSelect">
                            <option value="ocr">OCR模型 (速度快，仅识别文字)</option>
                            <option value="vl">VL模型 (速度慢，可描述图片内容)</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="formula_enable">🧮 公式识别</label>
                        <select id="formula_enable">
                            <option value="false">否 (速度快，跳过公式)</option>
                            <option value="true">是 (速度慢，可将公式转为LaTeX格式)</option>
                        </select>
                    </div>

                    <div id="fileNames">未选择文件</div>

                    <div class="upload-row">
                        <label for="fileInput" class="upload-btn">📁 选择文件</label>
                        <button type="submit">🚀 开始解析</button>
                    </div>

                    <input type="file" id="fileInput"
                        accept=".pdf,.docx,.doc,.pptx,.ppt,.jpeg,.png,.jpg">
                </form>
            </div>

            <div id="result">
                <div class="result-header">处理结果</div>
                <div id="status"></div>
                <div id="markdown-content"></div>
                <div style="text-align: center; margin-top: 20px;">
                    <button id="downloadBtn">💾 下载Markdown文件</button>
                </div>
            </div>
        </div>
    </div>

    <script src="mineru/js/marked.min.js"></script>
    <script>
        const fileInput = document.getElementById('fileInput');
        const fileNamesDiv = document.getElementById('fileNames');
        const uploadContainer = document.getElementById('uploadContainer');

        // 拖拽功能实现
        function handleDragOver(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadContainer.classList.add('drag-over');
        }

        function handleDragLeave(e) {
            e.preventDefault();
            e.stopPropagation();
            // 只有当鼠标真正离开容器时才移除样式
            if (!uploadContainer.contains(e.relatedTarget)) {
                uploadContainer.classList.remove('drag-over');
            }
        }

        function handleDrop(e) {
            e.preventDefault();
            e.stopPropagation();
            uploadContainer.classList.remove('drag-over');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                // 验证文件类型
                const file = files[0];
                const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                    'application/msword', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                                    'image/jpeg', 'image/png', 'image/jpg'];

                if (!allowedTypes.includes(file.type)) {
                    alert('错误：请上传PDF、Word、PPT或图片文件');
                    return;
                }

                // 将文件设置到文件输入框
                const dt = new DataTransfer();
                dt.items.add(file);
                fileInput.files = dt.files;

                // 触发文件输入框的change事件来更新显示
                fileInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }

        // 绑定拖拽事件
        uploadContainer.addEventListener('dragover', handleDragOver);
        uploadContainer.addEventListener('dragenter', handleDragOver);
        uploadContainer.addEventListener('dragleave', handleDragLeave);
        uploadContainer.addEventListener('drop', handleDrop);

        // 防止页面其他区域的拖拽默认行为
        document.addEventListener('dragover', function(e) {
            e.preventDefault();
        });
        document.addEventListener('drop', function(e) {
            e.preventDefault();
        });

        fileInput.addEventListener('change', function () {
            if (fileInput.files.length === 0) {
            fileNamesDiv.textContent = '未选择文件';
            } else {
            const names = Array.from(fileInput.files).map(f => f.name);
            fileNamesDiv.innerHTML = names.join('<br>');
            }
        });

        // 下载实例文件按钮点击事件
        document.getElementById('downloadSampleBtn').addEventListener('click', function() {
            // 创建一个隐藏的链接来触发下载
            const link = document.createElement('a');
            link.href = '/mineru/download-sample';
            link.download = 'simple.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });
